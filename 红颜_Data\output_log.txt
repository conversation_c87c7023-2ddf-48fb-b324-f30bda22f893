Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.049 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\HYYM\[PC]红颜赞助250813\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 62e0 -> priority: 1 
  Thread -> id: 661c -> priority: 1 
  Thread -> id: 6110 -> priority: 1 
  Thread -> id: 4e58 -> priority: 1 
UnloadTime: 2.136000 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.624100 ms (FindLiveObjects: 0.037300 ms CreateObjectMapping: 0.019600 ms MarkObjects: 0.548100 ms  DeleteObjects: 0.018400 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.557100 ms (FindLiveObjects: 0.018400 ms CreateObjectMapping: 0.013800 ms MarkObjects: 0.502900 ms  DeleteObjects: 0.021600 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

重复key:曼陀支线姬雪2,xml=storysPY.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

The referenced script on this Behaviour is missing!
 
(Filename:  Line: 1649)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.723300 ms

Unloading 52 unused Assets to reduce memory usage. Loaded Objects now: 887.
Total: 57.999100 ms (FindLiveObjects: 0.034400 ms CreateObjectMapping: 0.022800 ms MarkObjects: 57.899300 ms  DeleteObjects: 0.042000 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 2 unused Assets to reduce memory usage. Loaded Objects now: 885.
Total: 56.074200 ms (FindLiveObjects: 0.038300 ms CreateObjectMapping: 0.027500 ms MarkObjects: 55.976303 ms  DeleteObjects: 0.031000 ms)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.736300 ms

Unloading 31 unused Assets to reduce memory usage. Loaded Objects now: 552.
Total: 46.822800 ms (FindLiveObjects: 0.031100 ms CreateObjectMapping: 0.015300 ms MarkObjects: 46.746201 ms  DeleteObjects: 0.029500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 554.
Total: 45.508102 ms (FindLiveObjects: 0.025100 ms CreateObjectMapping: 0.015100 ms MarkObjects: 45.439701 ms  DeleteObjects: 0.027200 ms)

The file 'D:/QuarkNetdiskDownload/HYYM/[PC]红颜赞助250813/红颜_Data/level2' is corrupted! Remove it and launch unity again!
[Position out of bounds!]
 
(Filename:  Line: 241)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.547200 ms

Unloading 37 unused Assets to reduce memory usage. Loaded Objects now: 3579.
Total: 45.945099 ms (FindLiveObjects: 0.108000 ms CreateObjectMapping: 0.046400 ms MarkObjects: 45.748798 ms  DeleteObjects: 0.041000 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3579.
Total: 45.396400 ms (FindLiveObjects: 0.125500 ms CreateObjectMapping: 0.043700 ms MarkObjects: 45.222401 ms  DeleteObjects: 0.004000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 3579.
Total: 43.146202 ms (FindLiveObjects: 0.121700 ms CreateObjectMapping: 0.048800 ms MarkObjects: 42.948997 ms  DeleteObjects: 0.025900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3579.
Total: 43.935101 ms (FindLiveObjects: 0.095300 ms CreateObjectMapping: 0.043700 ms MarkObjects: 43.791302 ms  DeleteObjects: 0.004200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 13 unused Assets to reduce memory usage. Loaded Objects now: 3542.
Total: 47.145798 ms (FindLiveObjects: 0.118500 ms CreateObjectMapping: 0.048000 ms MarkObjects: 46.848000 ms  DeleteObjects: 0.130700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 8 unused Assets to reduce memory usage. Loaded Objects now: 3945.
Total: 43.588299 ms (FindLiveObjects: 0.121600 ms CreateObjectMapping: 0.040400 ms MarkObjects: 43.393700 ms  DeleteObjects: 0.031600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3945.
Total: 44.943600 ms (FindLiveObjects: 0.146000 ms CreateObjectMapping: 0.065900 ms MarkObjects: 44.726902 ms  DeleteObjects: 0.004100 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3945.
Total: 42.824001 ms (FindLiveObjects: 0.116100 ms CreateObjectMapping: 0.063500 ms MarkObjects: 42.638500 ms  DeleteObjects: 0.005300 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3945.
Total: 42.505100 ms (FindLiveObjects: 0.115800 ms CreateObjectMapping: 0.064300 ms MarkObjects: 42.319099 ms  DeleteObjects: 0.004700 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3951.
Total: 45.105301 ms (FindLiveObjects: 0.165800 ms CreateObjectMapping: 0.059200 ms MarkObjects: 44.875099 ms  DeleteObjects: 0.003800 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 3953.
Total: 44.166500 ms (FindLiveObjects: 0.144100 ms CreateObjectMapping: 0.045700 ms MarkObjects: 43.971798 ms  DeleteObjects: 0.004200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 14 unused Assets to reduce memory usage. Loaded Objects now: 3912.
Total: 41.340702 ms (FindLiveObjects: 0.101400 ms CreateObjectMapping: 0.040300 ms MarkObjects: 41.170799 ms  DeleteObjects: 0.027500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 4127.
Total: 42.943703 ms (FindLiveObjects: 0.123800 ms CreateObjectMapping: 0.038500 ms MarkObjects: 42.746799 ms  DeleteObjects: 0.033900 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 4127.
Total: 43.674202 ms (FindLiveObjects: 0.178300 ms CreateObjectMapping: 0.044800 ms MarkObjects: 43.445900 ms  DeleteObjects: 0.004400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 22 unused Assets to reduce memory usage. Loaded Objects now: 3923.
Total: 43.962299 ms (FindLiveObjects: 0.149000 ms CreateObjectMapping: 0.052100 ms MarkObjects: 43.724297 ms  DeleteObjects: 0.036000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 10 unused Assets to reduce memory usage. Loaded Objects now: 4130.
Total: 42.169498 ms (FindLiveObjects: 0.123800 ms CreateObjectMapping: 0.040000 ms MarkObjects: 41.977402 ms  DeleteObjects: 0.027600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 22 unused Assets to reduce memory usage. Loaded Objects now: 3925.
Total: 46.425800 ms (FindLiveObjects: 0.173400 ms CreateObjectMapping: 0.046300 ms MarkObjects: 46.169003 ms  DeleteObjects: 0.036200 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 10 unused Assets to reduce memory usage. Loaded Objects now: 4130.
Total: 42.080997 ms (FindLiveObjects: 0.156500 ms CreateObjectMapping: 0.059800 ms MarkObjects: 41.839996 ms  DeleteObjects: 0.024000 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 0 unused Assets to reduce memory usage. Loaded Objects now: 4157.
Total: 42.387600 ms (FindLiveObjects: 0.135700 ms CreateObjectMapping: 0.045100 ms MarkObjects: 42.201797 ms  DeleteObjects: 0.004500 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 23 unused Assets to reduce memory usage. Loaded Objects now: 3951.
Total: 44.216202 ms (FindLiveObjects: 0.167900 ms CreateObjectMapping: 0.053200 ms MarkObjects: 43.958298 ms  DeleteObjects: 0.035600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 10 unused Assets to reduce memory usage. Loaded Objects now: 4156.
Total: 44.834801 ms (FindLiveObjects: 0.121500 ms CreateObjectMapping: 0.039400 ms MarkObjects: 44.645802 ms  DeleteObjects: 0.027400 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 22 unused Assets to reduce memory usage. Loaded Objects now: 3951.
Total: 43.641800 ms (FindLiveObjects: 0.173300 ms CreateObjectMapping: 0.064000 ms MarkObjects: 43.367401 ms  DeleteObjects: 0.036400 ms)

